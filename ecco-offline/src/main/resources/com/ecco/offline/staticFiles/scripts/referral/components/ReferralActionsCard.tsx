import {
    Accordion,
    AccordionDetails,
    AccordionSummary, Box,
    <PERSON><PERSON>,
    Card,
    CardActions,
    CardContent,
    CardHeader,
    Grid,
    Typography
} from "@eccosolutions/ecco-mui";
import {ExpandMore} from "@eccosolutions/ecco-mui-controls"
import * as React from "react";
import {FC, useState, ReactNode} from "react";
import {ReferralDto, SessionData} from "ecco-dto";
import {
    LoadingOrError,
    Navigation,
    useClient,
    useNavigation,
    useServiceRecipientWithEntities,
    useServicesContext
} from "ecco-components";
import {componentAsElementWithServices} from "../../offline/ServicesContextProvider";
import {showCommunicationInModal} from "../../contacts/components/communicationControls";
import {ServiceType} from "ecco-dto/service-config-domain";
import {emergencyDetails} from "./EmergencyDetailsForm";
import {getReferralDirectConditionalHref} from "../../clientdetails/components/ClientReferralsPopup";
import {lookupEvidenceTasks} from "../../workflow/tasklist/TasksControl";
import {TaskWithTitle} from "ecco-dto/workflow-dto";
import {FlagPerServiceRecipient} from "../../service-recipients/StatusPanel";

export class Referrals {
    constructor(public referrals: ReferralDto[]) {
    }
}

interface Props {
    referral: ReferralDto
    requiresOnline?: boolean

}

export function linkToFile(navigation: Navigation, referral: ReferralDto, requiresOnline: boolean) {
    const srId = referral.serviceRecipientId;

    const referralUrl = getReferralDirectConditionalHref(referral, requiresOnline);

    return <Button
        key={referral.referralId} color="primary" variant="outlined" size="small"
        onClick={navigation.asCallback(`/referrals/${srId}/`)}
        href={navigation.asHref(referralUrl)}
    >goto file</Button>
}

export const ReferralsWithClient: FC<{referrals: Referrals, requiresOnline: boolean}> = ({referrals, requiresOnline}) => {
    return <ClientFromReferralCard referral={referrals.referrals[0]}>
            <Grid container spacing={1}>
                {referrals.referrals.map(r =>
                    <Grid key={r.referralId} item xs={12}>
                        <ReferralActionsCard key={r.referralId} referral={r} requiresOnline={requiresOnline}/>
                    </Grid>
                )}
            </Grid>
        </ClientFromReferralCard>
}

export function referralQuickActionsAsElement(referral: ReferralDto, requiresOnline: boolean) {
    return componentAsElementWithServices(<ReferralActionsCard referral={referral} requiresOnline={requiresOnline}/>, referral.referralId.toString() )
}

export function referralsAndClientAsElement(referrals: Referrals, requiresOnline: boolean) {
    return componentAsElementWithServices(<ReferralsWithClient referrals={referrals} requiresOnline={requiresOnline}/>, referrals.referrals[0].clientId.toString() )
}


export const ClientFromReferralCard: FC<{referral: ReferralDto}> = ({referral, children}) => {
    const {sessionData} = useServicesContext()
    const [expanded, setExpanded] = useState(false)
    const {client, error} = useClient(referral.clientId)
    if (!client) return <LoadingOrError error={error}/>

    const tabOrder = sessionData.getServiceTypeByServiceCategorisationId(referral.serviceAllocationId).getTaskDefinitionSetting("referralView", "tabOrder");
    const tabOrderHasCommunication = sessionData.getServiceTypeByServiceCategorisationId(referral.serviceAllocationId).taskDefinitionSettingHasFlag("referralView", "tabOrder", "communication");
    const hasTabOrderCommunication = !tabOrder || tabOrderHasCommunication;

    return <Grid item xs={12}>
        <Accordion expanded={expanded} variant="elevation" elevation={0}>
            <AccordionSummary onClick={() => setExpanded(!expanded)} expandIcon={<ExpandMore/>}>
                <Box >
                    <Typography variant="h6">{referral.clientDisplayName}</Typography>
                    {/* NB now shows the newer addressLocation - eg /clients/<id> PersonToViewModel */}
                    <Typography variant="body2">{client.address?.address && client.address?.address[0]}</Typography>
                    <Typography variant="body2">{client.address?.postcode}</Typography>
                    <Typography variant="body2">c-id: {referral.clientCode || referral.clientId}</Typography>
                </Box>
            </AccordionSummary>
            <AccordionDetails>
                <Grid container spacing={2}>
                    <Grid key="client" item xs={12}>
                        <Card elevation={0}>
                            <CardContent>
                                {client.mobileNumber && <div><a href={`tel:${client.mobileNumber}`}>{client.mobileNumber}</a></div>}
                                {client.phoneNumber && <div><a href={`tel:${client.phoneNumber}`}>{client.phoneNumber}</a></div>}
                                {client.email && <div><a href={`mailto:${client.email}`}>{client.email}</a></div>}
                            </CardContent>
                            <CardActions>
                                {hasTabOrderCommunication && sessionData.hasRoleFilePermissionForCommunication('r') &&
                                    <Button key="comms" color="primary" variant="outlined" size="small"
                                            onClick={() => showCommunicationInModal(referral.contactId)}>communication</Button>
                                }
                                <Button key="emerg" color="primary" variant="outlined" size="small"
                                        onClick={() => emergencyDetails(referral.serviceRecipientId, () => {
                                        }, true)}
                                >emergency details</Button>
                            </CardActions>
                        </Card>
                    </Grid>
                    {children}
                </Grid>
            </AccordionDetails>
        </Accordion>
    </Grid>
}

function getTasksNamesForButtons(serviceType: ServiceType, sessionData: SessionData) {
    const evidencePagesOnQuickLog = serviceType.findTaskNamesWhereSettingHasFlag("showOnQuickLog", "y")

    if (evidencePagesOnQuickLog.length) {
        return evidencePagesOnQuickLog;
    }

    return [serviceType.getFirstSupportTaskName(sessionData), serviceType.getFirstRiskTaskName(sessionData)].filter(x => x != null)
}

export const ReferralActionsCard = ({referral, requiresOnline}: Props) => {
    const {sessionData} = useServicesContext()
    const {context} = useServiceRecipientWithEntities(referral.serviceRecipientId);
    const navigation = useNavigation()
    const allocation = sessionData.getServiceCategorisationName(referral.serviceAllocationId)
    const serviceType = sessionData.getServiceTypeFromServiceCategorisation(referral.serviceAllocationId);
    const taskNamesForButtons = getTasksNamesForButtons(serviceType, sessionData);

    const [taskForm, setTaskForm] = useState<ReactNode>(null);

    return (
        <Card elevation={2}>
            <CardHeader
                title={`${allocation} (${sessionData.getDto().messages[referral.statusMessageKey]})`}
                subheader={`r-id: ${referral.referralCode || referral.referralId.toString()}`}
            />
            <CardContent>
                <FlagPerServiceRecipient srIdFile={referral.serviceRecipientId} serviceRecipient={referral}/>
                {/*<RiskFlagsAsTextList serviceRecipientId={referral.serviceRecipientId}/>*/}
            </CardContent>
            <CardActions>
                <Grid container spacing={1} direction="row">
                    {!sessionData.hasRoleSecurity() &&
                        <Grid item>
                            {linkToFile(navigation, referral, requiresOnline)}
                        </Grid>
                    }

                    {taskNamesForButtons.map(taskName =>
                        {
                            const title = serviceType.lookupTaskName(taskName);
                            const taskWithTitle = {title: "", taskName: taskName} as any as TaskWithTitle;
                            return <Grid key={taskName} item>
                                <Button key={taskName} color="primary" variant="outlined" size="small"
                                      /* we haven't got a <CommandForm here but TasksControl does */
                                      /* so we send an onFinished for native react controls in lookupEvidenceTasks to close the form */
                                      onClick={() => setTaskForm(lookupEvidenceTasks(context, taskWithTitle, () => setTaskForm(null)))}>{title}</Button>
                            </Grid>
                        }
                    )}
                    {taskForm}
                </Grid>
            </CardActions>
        </Card>
    );
};
