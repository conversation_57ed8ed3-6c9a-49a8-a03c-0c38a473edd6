import _ = require("lodash");
import * as React from "react"
import {Component, ComponentClass, ReactElement} from "react"
import {ResizeEvent, StringToObjectMap} from "@eccosolutions/ecco-common";
import {DelegateResponse, SessionData} from "ecco-dto";
import {<PERSON><PERSON>, Badge, Tab, Table, Tabs} from "react-bootstrap";
import {LoadingSpinner, SearchProps} from "ecco-components";

interface Props<T, CRITERIA> {

    sessionData: SessionData;

    /** Callback with the client/staff/building record each time a result is selected or created/changed - used in wizard
     */
    onChange?: (result: T, isNew?: boolean) => void;

    /** Component to use to render further results of the selected item - only required if selectedResultRender is true */
    selectedResultRenderer?: (result: T) => ReactElement;
    /** Use the selected renderer */
    selectedResultRender?: boolean;

    /** Provide search text here to skip the search */
    criteria?: CRITERIA;

    /** set false to allow subforms to render-over rather than popup */
    subFormsAsModal: boolean;

    /** enable/disable the 'new T' link */
    existingOnly?: boolean;
}

interface State<T, CRITERIA> {
    criteria: CRITERIA;
    newRecord: T;
    fetchingResults: boolean; // a load doesn't trigger a state change
    resultGroupIndex: Record<string, T[]>;
    resultGroupErrors?: Record<string, string>;
    alert: any;
    subForm: { detail?: boolean };
    tabSelected: string;
}

export abstract class SearchAndImportList<T, CRITERIA> extends Component<Props<T, CRITERIA>, State<T, CRITERIA>> {

    /**
     * @param props
     * @param SearchInputComponent
     * ComponentClass - A component that provides a UI that fits the required input
     * @param keyByField
     * @param resultName
     */
    // we want this for keyByField but... nope
    // type PickNumericProperties<T> = Pick<T, keyof {[k: keyof T]: number}>
    protected constructor(
        props,
        private SearchInputComponent: ComponentClass<SearchProps<CRITERIA>>,
        private keyByField: keyof T,
        private resultName: string // e.g. client
    ) {
        super(props);

        const criteria = this.props.criteria || {} as CRITERIA;
        const resultNew = this.props.criteria ? this.prepareNewResult(this.props.criteria || criteria, null) : null;

        // we can't simply call handleNewTClick because its 'setState' doesn't set the state at this point in the flow
        this.state = {
            criteria: criteria,
            newRecord: resultNew,
            fetchingResults: false,
            resultGroupIndex: {},
            alert: {},
            subForm: this.props.criteria ? {'detail': true} : {},
            tabSelected: 'ecco'
        };

    }

    protected abstract getNewT(): T;

    protected abstract prepareNewResult(criteria: CRITERIA, result?: T): T;

    protected abstract matchOnExternalRef(result: T, entry: T);

    protected abstract queryForResults(criteria: CRITERIA): Promise<StringToObjectMap<DelegateResponse<T[]>>>;

    protected abstract getDetailSubForm();

    protected abstract getResultHeadings(): ReactElement;

    protected abstract getRemoteResultRenderer(): (result: T) => ReactElement;

    protected abstract getEccoResultRenderer(): (result: T) => ReactElement;

    protected abstract handleMinimisedNewTClick(resultToImport: T);


    /**
     * Determine what to do on a click of search/back icon, or enter.
     * Any change performs a search, or clears a search. However, if we have keyPressed,
     * then it may be we need to prompt an immediate search
     *
     * @param criteria Current criteria from the search bar
     * @param enterPressed Whether this criteria represents a new search ('enter' could expect a new search)
     */
    private handleSearchTrigger = (criteria: CRITERIA, enterPressed: boolean) => {
        // Workaround for Enter key giving two events back - as well as multiple clicks
        if (this.state.fetchingResults) {
            console.debug("skipped search as we'd not finished previous");
            return;
        }

        // if not currently doing AJAX and we have results - clear the results
        // because either the button was clicked or enter pressed, whilst results showing
        if (this.showingSearchResult()) {

            // give a chance to show new results if there was a change to the search with a 'enterPressed'
            const criteriaDifferent = !_.isEqual(criteria, this.state.criteria);
            const loadResults = enterPressed && criteriaDifferent
                ? () => this.loadResults(criteria)
                : () => {};

            this.setState({
                resultGroupIndex: {},
                alert: {}
            }, loadResults);
            return;
        }

        // if search clicked/entered
        this.loadResults(criteria);

    };

    private loadResults(criteria: CRITERIA) {
        this.setState({
            fetchingResults: true
        });

        this.queryForResults(criteria).then(response => {

            const resultGroupIndex: Record<string, T[]> = {};
            const resultGroupErrors: Record<string, string> = {};
            let found = false;
            let tabSelected: string = null;

            for (let groupName in response) {
                if (!response.hasOwnProperty(groupName)) {
                    continue;
                }
                if (response[groupName].statusCode == 200) {
                    const results: T[] = response[groupName].payload;
                    found = found || results.length > 0;
                    tabSelected = tabSelected || (results.length > 0 && groupName);

                    resultGroupIndex[groupName] = results;
                } else {
                    resultGroupErrors[groupName] = response[groupName].statusText || ' failed with an unknown error';
                }
            }

            this.setState({
                fetchingResults: false,
                resultGroupIndex,
                resultGroupErrors,
                criteria,
                alert: !found && {notFound: true} || {found: true},
                tabSelected: tabSelected || 'ecco'
            });
        })
    }

    private showingSearchResult() {
        return _.size(this.state.alert) > 0;
    }

    protected handleEccoClick(result: T) {
        this.props.onChange && this.props.onChange(result);
    }

    protected handleSave(result: T) {
        this.props.onChange && this.props.onChange(result, true);

        this.setState({
            resultGroupIndex: {'ecco': [result]},
            subForm: {}
        });
    }

    protected handleNewTClick(resultToImport?: T) {
        const newRecord = this.prepareNewResult(this.state.criteria, resultToImport);
        this.setState({
            newRecord,
            subForm: {'detail': true}
        });
        return;
    }

    protected handleSubFormCancel = () => this.setState({subForm: {}});

    private handleTabSelect = tabName => this.setState({tabSelected: tabName});

    public alreadyImported(remoteResult: T) {
        return _.some(this.state.resultGroupIndex['ecco'],
            entry => this.matchOnExternalRef(remoteResult, entry));
    };

    render() {
        let TabsElement;

        const AlertElement = Object.entries(this.state.alert)
            .map(([style, message]) => {
                switch (style) {
                    case 'notFound':
                        return (
                            <div key={style} className="page-header">
                                <h3>no matches found {!this.props.existingOnly && <small>
                                  <a
                                    onClick={() => this.handleNewTClick()}>
                                    <span>create a new one</span>
                                  </a>, or try another search.</small>}
                                </h3>
                            </div>
                        );
                    case 'found':
                        return (
                            <div key={style} className="page-header">
                                <h3>matches found {!this.props.existingOnly && <small>
                                  <span>choose one or </span>
                                  <a
                                    onClick={() => this.handleNewTClick()}>
                                    <span>create a new one</span>
                                  </a>, or try another search.</small>}
                                </h3>
                            </div>
                        );
                    default:
                        return (
                            <div key={style} className="page-header">
                                <h3>{this.resultName}
                                    <small>{message}</small>
                                </h3>
                            </div>
                        );
                }
            });

        //noinspection JSMismatchedCollectionQueryUpdate - used in JSX but inspection doesn't spot that
        const TabElements: ReactElement[] = [];
        _.forEach(this.state.resultGroupIndex, (resultsRaw: T[], groupName: string) => {
            // could try sorting by property: https://stackoverflow.com/questions/66815720/typescript-generic-sort-array-of-objects-by-dynamic-property-name
            const results = resultsRaw.sort((a,b) => `${a[this.keyByField]}`.localeCompare(`${b[this.keyByField]}`))
            let TableElement;

            const numResultsInGroup = results.length;
            const TabTitle = (
                <span>{groupName} <Badge>{numResultsInGroup}</Badge></span>
            );

            if (groupName == 'ecco') {
                TableElement = (
                    <Table hover>
                        <thead>
                        <tr>
                            {this.getResultHeadings()}
                        </tr>
                        </thead>
                        <tbody>
                        {results.map(this.getEccoResultRenderer())}
                        </tbody>
                    </Table>
                );
            } else {
                TableElement = (
                    <Table hover>
                        <thead>
                        <tr>
                            {this.getResultHeadings()}
                            <th/>
                        </tr>
                        </thead>
                        <tbody>
                        {results.map(this.getRemoteResultRenderer())}
                        </tbody>
                    </Table>
                );
            }

            TabElements.push(
                <Tab
                    key={groupName}
                    title={TabTitle as any}
                    eventKey={groupName}
                    disabled={numResultsInGroup == 0}>
                    {TableElement}
                </Tab>
            );
        });

        _.forEach(this.state.resultGroupErrors, (message: string, groupName: string) => {
            const TabTitle = (
                <span>{groupName} <Badge>?</Badge></span>
            );
            TabElements.push(
                <Tab
                    key={groupName}
                    title={TabTitle as any}
                    eventKey={groupName}>
                    <div className="container-fluid top-gap-15">
                        <Alert>{message}</Alert>
                    </div>
                </Tab>
            );
        });
        TabsElement = TabElements.length > 0 ? (
            <Tabs
                id="search-results"
                animation={false}
                activeKey={this.state.tabSelected}
                onSelect={(tabName) => this.handleTabSelect(tabName)}>
                {TabElements}
            </Tabs>
        )
            : <span>search for {this.resultName}</span>;

        const criteria = this.state.criteria || {} as CRITERIA;
        const SearchBar = <div key='client-search' className='row'>
            <div className='col-xs-12'>
                {this.props.selectedResultRender ? null :
                    (<div className="page-header">
                        enter the {this.resultName} name to start by checking if they already exist on the system
                    </div>)}
                <this.SearchInputComponent
                    resultName={this.resultName}
                    criteria={criteria}
                    onTrigger={this.handleSearchTrigger}
                    searchActive={this.showingSearchResult()}
                />
                {this.state.fetchingResults &&
                    <LoadingSpinner/>
                }
                {AlertElement}
                {TabsElement}
            </div>
        </div>;

        const FormsToShow: ReactElement[] = [];
        if (this.props.subFormsAsModal) {
            FormsToShow.push(SearchBar);
            FormsToShow.push(this.getDetailSubForm());
        } else {
            if (this.state.subForm.detail) {
                FormsToShow.push(this.getDetailSubForm());
            } else {
                FormsToShow.push(SearchBar);
            }
        }

        return (
            <div>
                {FormsToShow}
            </div>
        );
    }

// when 'create a new one' address is selected, the window doesn't add a scroll bar for the new fields
    // meaning the 'asve' is off the bottom and can't be located.
    // so we just do a render (to capture when the state is changed to show a DetailSubForm)
    public componentDidMount() {
        // a true lifecycle event doesn't exist in react, may need an ugly timeout
        // see https://stackoverflow.com/questions/26556436/react-after-render-code
        setTimeout(ResizeEvent.bus.fire, 30);
        // ResizeEvent.bus.fire(new ResizeEvent());
    }

}